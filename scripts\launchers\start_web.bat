@echo off
chcp 65001 >nul
title 亚马逊图片上传图床工具 - Web服务启动器

echo.
echo ==========================================
echo   亚马逊图片上传图床工具 - Web服务启动器
echo   🚀 Amazon Image Upload Tool Web Launcher
echo ==========================================
echo.

cd /d "%~dp0..\.."

echo 🔍 检查虚拟环境...
if not exist "amazon_env\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在！
    echo 请先运行以下命令创建虚拟环境：
    echo   python -m venv amazon_env
    echo   amazon_env\Scripts\activate
    echo   python -m pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ 虚拟环境已找到
echo 🚀 启动Web服务...

amazon_env\Scripts\python.exe main.py --web

echo.
echo 👋 Web服务已停止
pause
