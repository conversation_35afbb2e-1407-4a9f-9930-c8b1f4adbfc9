@echo off
chcp 65001 >nul
title 亚马逊图片上传图床工具 - 测试运行器

echo.
echo ==========================================
echo   亚马逊图片上传图床工具 - 测试运行器
echo   🧪 Amazon Image Upload Tool Test Runner
echo ==========================================
echo.

cd /d "%~dp0..\.."

echo 🔍 检查虚拟环境...
if not exist "amazon_env\Scripts\python.exe" (
    echo ❌ 虚拟环境不存在！
    pause
    exit /b 1
)

echo ✅ 虚拟环境已找到
echo 🧪 运行测试套件...

amazon_env\Scripts\python.exe main.py --test

echo.
echo 👋 测试完成
pause
