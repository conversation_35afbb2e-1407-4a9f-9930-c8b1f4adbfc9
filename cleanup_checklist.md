# 🧹 项目清理文件清单

## 📋 需要清理的文件和目录

### 1. 🔄 重复的主程序脚本

#### ✅ 保留（主要入口）
- `main.py` - 统一主入口（新创建，功能完整）
- `src/web_app.py` - 主Web应用

#### ❌ 建议删除（重复功能）
- `final_test.py` - 测试脚本，已被 `main.py --test` 替代
- `scripts/quick_start.py` - 快速启动脚本，已被 `main.py` 替代
- `scripts/start_web.py` - 空文件（只有1行），无实际功能
- `src/web/app.py` - 重复的Web应用，与 `src/web_app.py` 功能重复

### 2. 🗂️ 冗余的批处理文件

#### ✅ 保留（有用的）
- `scripts/windows/启动Web服务-动态端口.bat` - 最新版本，功能完整

#### ❌ 建议删除（过时或重复）
- `scripts/windows/启动Web服务.bat` - 旧版本
- `scripts/windows/启动Web服务-新版.bat` - 中间版本
- `scripts/windows/重启Web服务-测试修复.bat` - 临时测试文件
- `scripts/launchers/start_web.bat` - 重复功能
- `scripts/launchers/run_tests.bat` - 重复功能

### 3. 🔧 临时调试脚本

#### ❌ 建议删除（临时文件）
- `scripts/check_bootstrap_status.py` - Bootstrap状态检查，临时调试用
- `scripts/check_web_service.py` - Web服务检查，临时调试用
- `scripts/fix_incognito_compatibility.py` - 隐私模式兼容性修复，临时用

### 4. 📦 构建相关文件

#### ❌ 建议删除（过时的构建文件）
- `optimized_minimal.spec` - 旧的PyInstaller规格文件

### 5. 📁 空目录和缓存

#### ❌ 建议删除（如果为空）
- `scripts/launchers/` - 如果清理后为空目录
- `__pycache__/` - Python缓存目录
- `src/__pycache__/` - Python缓存目录
- `src/web/routes/__pycache__/` - Python缓存目录

### 6. 🧪 测试和示例文件

#### ⚠️ 需要确认（可能有用）
- `scripts/quick_test_report_format.py` - 报告格式测试工具
- `scripts/create_test_classification_report.py` - 创建测试报告
- `scripts/analyze_field_mapping.py` - 字段映射分析
- `src/tools/debug_report_columns.py` - 报告列调试工具

## 📊 清理统计

### 🗑️ 确定删除的文件（9个）
1. `final_test.py`
2. `scripts/quick_start.py`
3. `scripts/start_web.py`
4. `src/web/app.py`
5. `scripts/check_bootstrap_status.py`
6. `scripts/check_web_service.py`
7. `scripts/fix_incognito_compatibility.py`
8. `optimized_minimal.spec`

### 🗂️ 确定删除的批处理文件（5个）
1. `scripts/windows/启动Web服务.bat`
2. `scripts/windows/启动Web服务-新版.bat`
3. `scripts/windows/重启Web服务-测试修复.bat`
4. `scripts/launchers/start_web.bat`
5. `scripts/launchers/run_tests.bat`

### 📁 可能删除的目录（3个）
1. `scripts/launchers/` - 如果清理后为空
2. `__pycache__/` - 缓存目录
3. `src/web/routes/__pycache__/` - 缓存目录

## ⚠️ 需要您确认的文件

### 🧪 测试工具（建议保留）
- `scripts/quick_test_report_format.py` - 用于测试商品分类报告格式
- `scripts/create_test_classification_report.py` - 创建测试数据
- `scripts/analyze_field_mapping.py` - 分析字段映射关系

### 🔧 开发工具（建议保留）
- `scripts/clean_build.py` - 构建清理工具
- `scripts/download_offline_resources.py` - 离线资源下载
- `src/tools/debug_report_columns.py` - 调试工具

## 🎯 清理后的项目结构

```
📦 亚马逊图片上传图床工具/
├── 📄 main.py                    # ✅ 统一主入口
├── 📄 project_config.py          # ✅ 项目配置
├── 📁 src/                       # 核心源码
│   ├── 📄 web_app.py            # ✅ 主Web应用
│   ├── 📁 core/                 # 核心功能模块
│   ├── 📁 tools/                # 开发工具
│   └── 📁 web/                  # Web资源（模板、静态文件）
├── 📁 scripts/                  # 工具脚本
│   ├── 📁 build/                # 构建脚本
│   ├── 📁 windows/              # Windows批处理（保留有用的）
│   └── 📄 *.py                  # 有用的工具脚本
├── 📁 tests/                    # 正式测试套件
└── 📁 docs/                     # 文档
```

## 💡 清理建议

1. **立即删除** - 确定无用的重复文件（14个文件）
2. **保留** - 有实际功能的测试和开发工具
3. **清理缓存** - Python缓存目录可以安全删除
4. **更新配置** - 更新 `project_config.py` 中的文件引用

---

**请确认是否同意按此清单进行清理？** 🤔
