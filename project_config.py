#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目配置文件
统一管理项目的各种配置参数
"""

import os
from pathlib import Path

# 项目基本信息
PROJECT_NAME = "亚马逊图片上传图床工具"
PROJECT_VERSION = "2.0"
PROJECT_DESCRIPTION = "Amazon Image Upload Tool - 专业的图片上传和管理工具"

# 路径配置
PROJECT_ROOT = Path(__file__).parent
SRC_DIR = PROJECT_ROOT / "src"
SCRIPTS_DIR = PROJECT_ROOT / "scripts"
TESTS_DIR = PROJECT_ROOT / "tests"
DOCS_DIR = PROJECT_ROOT / "docs"
CONFIG_DIR = PROJECT_ROOT / "config"
DATA_DIR = PROJECT_ROOT / "data"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "temp"

# Web服务配置
DEFAULT_WEB_PORT = 5000
WEB_HOST = "0.0.0.0"
WEB_DEBUG = True

# 主要入口文件配置
MAIN_WEB_APP = SRC_DIR / "web_app.py"
BACKUP_WEB_APP = SRC_DIR / "web" / "app.py"

# 脚本文件映射
SCRIPT_MAPPING = {
    "web": MAIN_WEB_APP,
    # 以下文件已清理：
    # "debug": PROJECT_ROOT / "debug_api.py",  # 已删除，使用 main.py --api-debug
    # "test": PROJECT_ROOT / "final_test.py",  # 已删除，使用 main.py --test
    # "quick_start": SCRIPTS_DIR / "quick_start.py"  # 已删除，使用 main.py
}

# 依赖检查
REQUIRED_FILES = [
    "src/web_app.py",
    "src/core/port_manager.py", 
    "config/mohe_config.py",
    "requirements.txt"
]

REQUIRED_DIRS = [
    "src",
    "config", 
    "data",
    "output"
]

def get_project_info():
    """获取项目基本信息"""
    return {
        "name": PROJECT_NAME,
        "version": PROJECT_VERSION,
        "description": PROJECT_DESCRIPTION,
        "root": str(PROJECT_ROOT)
    }

def validate_project_structure():
    """验证项目结构完整性"""
    missing_files = []
    missing_dirs = []
    
    # 检查必需文件
    for file_path in REQUIRED_FILES:
        full_path = PROJECT_ROOT / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    # 检查必需目录
    for dir_path in REQUIRED_DIRS:
        full_path = PROJECT_ROOT / dir_path
        if not full_path.exists():
            missing_dirs.append(dir_path)
    
    return {
        "valid": len(missing_files) == 0 and len(missing_dirs) == 0,
        "missing_files": missing_files,
        "missing_dirs": missing_dirs
    }

def get_active_web_app():
    """获取当前活跃的Web应用路径"""
    if MAIN_WEB_APP.exists():
        return MAIN_WEB_APP
    elif BACKUP_WEB_APP.exists():
        return BACKUP_WEB_APP
    else:
        return None

def print_project_status():
    """打印项目状态信息"""
    info = get_project_info()
    validation = validate_project_structure()
    
    print(f"📦 项目: {info['name']} v{info['version']}")
    print(f"📁 根目录: {info['root']}")
    print(f"🔧 结构完整性: {'✅ 完整' if validation['valid'] else '❌ 不完整'}")
    
    if validation['missing_files']:
        print(f"❌ 缺失文件: {', '.join(validation['missing_files'])}")
    
    if validation['missing_dirs']:
        print(f"❌ 缺失目录: {', '.join(validation['missing_dirs'])}")
    
    active_app = get_active_web_app()
    if active_app:
        print(f"🌐 活跃Web应用: {active_app.name}")
    else:
        print("❌ 未找到可用的Web应用")

if __name__ == "__main__":
    print_project_status()
