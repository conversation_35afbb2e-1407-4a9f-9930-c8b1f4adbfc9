# 🚀 亚马逊图片上传图床工具 - 启动指南

## 📋 项目结构优化说明

为了解决项目中存在多个主程序脚本的问题，我们进行了以下优化：

### ✅ 优化前的问题
- 存在多个Web应用入口：`src/web_app.py` 和 `src/web/app.py`
- 多个启动脚本：PowerShell脚本、Python脚本等
- 测试和调试脚本混杂在根目录

### 🎯 优化后的解决方案

#### 1. 统一主入口
现在只需要使用 **`main.py`** 作为唯一入口点：

```bash
# 启动Web服务（默认）
python main.py

# 启动Web服务并指定端口
python main.py --web --port 8080

# 运行测试套件
python main.py --test

# 调试API功能
python main.py --api-debug

# 显示系统状态
python main.py --status

# 启用调试模式
python main.py --debug
```

#### 2. 项目配置统一管理
- 新增 `project_config.py` 统一管理项目配置
- 自动检测项目结构完整性
- 智能选择可用的Web应用

#### 3. 推荐的启动方式

**🌟 日常使用（推荐）：**
```bash
python main.py
```

**🔧 开发调试：**
```bash
python main.py --debug
```

**🧪 功能测试：**
```bash
python main.py --test
```

## 📁 新的目录结构

```
📦 亚马逊图片上传图床工具/
├── 📄 main.py                    # ✅ 统一主入口
├── 📄 project_config.py          # ✅ 项目配置
├── 📄 STARTUP_GUIDE.md           # ✅ 启动指南
├── 📁 src/                       # 核心源码
│   ├── 📄 web_app.py            # 主Web应用
│   ├── 📁 core/                 # 核心功能模块
│   └── 📁 web/                  # Web相关模块
├── 📁 scripts/                  # 工具脚本
│   ├── 📁 dev/                  # 开发工具（建议）
│   └── 📁 build/                # 构建脚本
├── 📁 tests/                    # 正式测试套件
└── 📁 docs/                     # 文档
```

## 🔄 迁移指南

### 如果您之前使用：
- `start_web_service.ps1` → 现在使用：`python main.py`
- `scripts/quick_start.py` → 现在使用：`python main.py`
- `debug_api.py` → 现在使用：`python main.py --api-debug`
- `final_test.py` → 现在使用：`python main.py --test`

### 虚拟环境启动：
```bash
# 激活虚拟环境
amazon_env\Scripts\activate

# 启动应用
python main.py
```

## 🛠️ 故障排除

### 常见问题：

1. **找不到模块错误**
   ```bash
   python main.py --status  # 检查项目结构
   ```

2. **端口被占用**
   ```bash
   python main.py --port 8080  # 使用其他端口
   ```

3. **配置文件缺失**
   ```bash
   python main.py --status  # 查看缺失的文件
   ```

## 📞 技术支持

如果遇到问题，请：
1. 首先运行 `python main.py --status` 检查系统状态
2. 查看控制台输出的错误信息
3. 确保虚拟环境已正确安装依赖

---

**🎉 现在您可以使用统一的 `python main.py` 命令启动所有功能！**
