#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - 统一主入口
提供统一的启动方式和参数管理
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def print_banner():
    """打印应用横幅"""
    print("=" * 60)
    print("🚀 亚马逊图片上传图床工具 v2.0")
    print("📸 Amazon Image Upload Tool")
    print("=" * 60)

def start_web_service(port=None, debug=False):
    """启动Web服务"""
    print("🌐 启动Web服务...")
    
    try:
        # 导入主Web应用
        from src.web_app import app
        from src.core.port_manager import create_flask_port_manager
        
        # 创建端口管理器
        port_manager = create_flask_port_manager(
            app_name="亚马逊图片上传图床工具", 
            preferred_port=port or 5000
        )
        
        # 使用动态端口启动
        port_manager.run_with_dynamic_port(
            app,
            host='0.0.0.0',
            debug=debug,
            threaded=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)

def run_tests():
    """运行测试套件"""
    print("🧪 运行测试套件...")
    
    try:
        import subprocess
        
        # 运行主要功能测试
        test_files = [
            "tests/test_image_renamer.py",
            "final_test.py"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"🔍 运行测试: {test_file}")
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=False)
                if result.returncode != 0:
                    print(f"❌ 测试失败: {test_file}")
                else:
                    print(f"✅ 测试通过: {test_file}")
            else:
                print(f"⚠️ 测试文件不存在: {test_file}")
                
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def debug_api():
    """调试API功能"""
    print("🔧 启动API调试...")
    
    try:
        from debug_api import test_simple_request
        test_simple_request()
    except ImportError as e:
        print(f"❌ 调试模块导入失败: {e}")

def show_status():
    """显示系统状态"""
    print("📊 系统状态检查...")
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查关键文件
    critical_files = [
        "src/web_app.py",
        "src/core/port_manager.py",
        "config/mohe_config.py",
        "requirements.txt"
    ]
    
    print("\n📁 关键文件检查:")
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
    
    # 检查虚拟环境
    print(f"\n🔧 虚拟环境: {sys.prefix}")
    
    # 检查端口占用
    try:
        from src.core.port_manager import PortManager
        port_manager = PortManager()
        port_status = port_manager.check_port_status(5000)
        print(f"🌐 端口5000状态: {port_status}")
    except ImportError:
        print("⚠️ 无法检查端口状态")

def main():
    """主函数 - 统一入口点"""
    print_banner()
    
    parser = argparse.ArgumentParser(
        description="亚马逊图片上传图床工具 - 统一启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动Web服务（默认）
  python main.py --web              # 启动Web服务
  python main.py --web --port 8080  # 指定端口启动Web服务
  python main.py --test             # 运行测试
  python main.py --debug            # 调试API
  python main.py --status           # 显示系统状态
        """
    )
    
    parser.add_argument('--web', action='store_true', 
                       help='启动Web服务（默认模式）')
    parser.add_argument('--port', type=int, default=5000,
                       help='指定Web服务端口（默认: 5000）')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--test', action='store_true',
                       help='运行测试套件')
    parser.add_argument('--api-debug', action='store_true',
                       help='调试API功能')
    parser.add_argument('--status', action='store_true',
                       help='显示系统状态')
    
    args = parser.parse_args()
    
    # 如果没有指定任何参数，默认启动Web服务
    if not any([args.web, args.test, args.api_debug, args.status]):
        args.web = True
    
    try:
        if args.status:
            show_status()
        elif args.test:
            run_tests()
        elif args.api_debug:
            debug_api()
        elif args.web:
            start_web_service(port=args.port, debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序已退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
