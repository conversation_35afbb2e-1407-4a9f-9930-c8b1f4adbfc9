#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - 统一主入口
提供统一的启动方式和参数管理
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入项目配置
from project_config import (
    PROJECT_NAME, PROJECT_VERSION,
    get_project_info, DEFAULT_WEB_PORT
)

def print_banner():
    """打印应用横幅"""
    info = get_project_info()
    print("=" * 60)
    print(f"🚀 {info['name']} v{info['version']}")
    print(f"📸 {info['description']}")
    print("=" * 60)

def start_web_service(port=None, debug=False):
    """启动Web服务"""
    print("🌐 启动Web服务...")

    try:
        # 导入主Web应用
        from src.web_app import app
        print("✅ Web应用模块导入成功")

        # 导入端口管理器
        from src.core.port_manager import create_flask_port_manager
        print("✅ 端口管理器导入成功")

        # 创建端口管理器
        port_manager = create_flask_port_manager(
            app_name=PROJECT_NAME,
            preferred_port=port or DEFAULT_WEB_PORT
        )
        print("✅ 端口管理器创建成功")

        # 启动Web服务
        print("🚀 正在启动Web服务...")
        print("💡 提示: 按 Ctrl+C 停止服务")

        port_manager.run_with_dynamic_port(
            app,
            host='0.0.0.0',
            debug=debug,
            threaded=True,
            use_reloader=False
        )

        return True

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保已安装所有依赖: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        if debug:
            import traceback
            traceback.print_exc()
        return False

def run_tests():
    """运行测试套件"""
    print("🧪 运行测试套件...")

    try:
        import subprocess

        # 运行现有的测试文件
        test_files = [
            "tests/test_image_renamer.py",
        ]

        # 查找其他测试文件
        if os.path.exists("tests"):
            for file in os.listdir("tests"):
                if file.startswith("test_") and file.endswith(".py"):
                    test_path = os.path.join("tests", file)
                    if test_path not in test_files:
                        test_files.append(test_path)

        if not test_files:
            print("⚠️ 未找到测试文件")
            print("💡 您可以在 tests/ 目录下创建测试文件")
            return

        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"🔍 运行测试: {test_file}")
                result = subprocess.run([sys.executable, test_file],
                                      capture_output=False)
                if result.returncode != 0:
                    print(f"❌ 测试失败: {test_file}")
                else:
                    print(f"✅ 测试通过: {test_file}")
            else:
                print(f"⚠️ 测试文件不存在: {test_file}")

    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def debug_api():
    """调试API功能"""
    print("🔧 启动API调试...")

    # 简单的API测试功能
    try:
        import requests
        import json

        print("🔍 测试简单的API请求...")

        test_data = {
            "urls": [
                {
                    "filename": "B07XXXXX_MAIN.jpg",
                    "url": "https://example.com/test.jpg"
                }
            ]
        }

        print(f"📤 发送数据: {json.dumps(test_data, indent=2)}")

        response = requests.post(
            'http://localhost:5000/api/parse-urls',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=10
        )

        print(f"📡 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result}")
        else:
            print(f"❌ 失败: {response.text}")

    except Exception as e:
        print(f"❌ API调试异常: {str(e)}")
        print("💡 请确保Web服务正在运行: python main.py --web")

def show_status():
    """显示系统状态"""
    print("📊 系统状态检查...")
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查关键文件
    critical_files = [
        "src/web_app.py",
        "src/core/port_manager.py",
        "config/mohe_config.py",
        "requirements.txt"
    ]
    
    print("\n📁 关键文件检查:")
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
    
    # 检查虚拟环境
    print(f"\n🔧 虚拟环境: {sys.prefix}")
    
    # 检查端口占用
    try:
        from src.core.port_manager import PortManager
        port_manager = PortManager()
        port_status = port_manager.check_port_status(5000)
        print(f"🌐 端口5000状态: {port_status}")
    except ImportError:
        print("⚠️ 无法检查端口状态")

def main():
    """主函数 - 统一入口点"""
    print_banner()
    
    parser = argparse.ArgumentParser(
        description="亚马逊图片上传图床工具 - 统一启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动Web服务（默认）
  python main.py --web              # 启动Web服务
  python main.py --web --port 8080  # 指定端口启动Web服务
  python main.py --test             # 运行测试
  python main.py --debug            # 调试API
  python main.py --status           # 显示系统状态
        """
    )
    
    parser.add_argument('--web', action='store_true', 
                       help='启动Web服务（默认模式）')
    parser.add_argument('--port', type=int, default=5000,
                       help='指定Web服务端口（默认: 5000）')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--test', action='store_true',
                       help='运行测试套件')
    parser.add_argument('--api-debug', action='store_true',
                       help='调试API功能')
    parser.add_argument('--status', action='store_true',
                       help='显示系统状态')
    
    args = parser.parse_args()
    
    # 如果没有指定任何参数，默认启动Web服务
    if not any([args.web, args.test, args.api_debug, args.status]):
        args.web = True
    
    try:
        success = False

        if args.status:
            show_status()
            success = True
        elif args.test:
            run_tests()
            success = True
        elif args.api_debug:
            debug_api()
            success = True
        elif args.web:
            success = start_web_service(port=args.port, debug=args.debug)

        if not success and args.web:
            print("\n❌ Web服务启动失败")
            print("🔧 故障排除建议:")
            print("1. 检查端口是否被占用: python main.py --status")
            print("2. 尝试使用其他端口: python main.py --port 8080")
            print("3. 检查依赖安装: pip install -r requirements.txt")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n👋 用户中断，程序已退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
