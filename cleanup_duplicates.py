#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理重复脚本和无用测试脚本
"""

import os
import shutil
from pathlib import Path

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f"🧹 {title}")
    print(f"{'='*60}")

def safe_remove(path):
    """安全删除文件或目录"""
    try:
        if os.path.isfile(path):
            os.remove(path)
            print(f"✅ 删除文件: {path}")
            return True
        elif os.path.isdir(path):
            shutil.rmtree(path)
            print(f"✅ 删除目录: {path}")
            return True
        else:
            print(f"⚠️ 路径不存在: {path}")
            return False
    except Exception as e:
        print(f"❌ 删除失败 {path}: {e}")
        return False

def cleanup_duplicate_web_apps():
    """清理重复的Web应用"""
    print_section("清理重复的Web应用")
    
    # 保留 src/web_app.py 作为主应用
    # 删除 src/web/app.py 作为重复应用
    duplicate_web_app = "src/web/app.py"
    
    if os.path.exists(duplicate_web_app):
        print(f"🔍 发现重复的Web应用: {duplicate_web_app}")
        print("📱 保留主应用: src/web_app.py")
        safe_remove(duplicate_web_app)
    else:
        print("✅ 未发现重复的Web应用")

def cleanup_obsolete_scripts():
    """清理过时的脚本"""
    print_section("清理过时的脚本")
    
    obsolete_scripts = [
        "final_test.py",  # 已被 main.py --test 替代
        "scripts/quick_start.py",  # 已被 main.py 替代
        "scripts/start_web.py",  # 空文件，已被 main.py 替代
    ]
    
    for script in obsolete_scripts:
        if os.path.exists(script):
            print(f"🗑️ 清理过时脚本: {script}")
            safe_remove(script)
        else:
            print(f"✅ 脚本已不存在: {script}")

def cleanup_redundant_bat_files():
    """清理冗余的批处理文件"""
    print_section("清理冗余的批处理文件")
    
    # 保留一些有用的批处理文件，删除重复的
    redundant_bats = [
        "scripts/windows/启动Web服务.bat",  # 旧版本
        "scripts/windows/启动Web服务-新版.bat",  # 中间版本
        "scripts/windows/重启Web服务-测试修复.bat",  # 临时文件
        "scripts/launchers/start_web.bat",  # 重复功能
        "scripts/launchers/run_tests.bat",  # 重复功能
    ]
    
    for bat_file in redundant_bats:
        if os.path.exists(bat_file):
            print(f"🗑️ 清理冗余批处理: {bat_file}")
            safe_remove(bat_file)
        else:
            print(f"✅ 批处理已不存在: {bat_file}")

def cleanup_debug_scripts():
    """清理调试脚本"""
    print_section("清理调试脚本")
    
    debug_scripts = [
        "scripts/check_bootstrap_status.py",  # 临时调试脚本
        "scripts/check_web_service.py",  # 临时调试脚本
        "scripts/fix_incognito_compatibility.py",  # 临时修复脚本
    ]
    
    for script in debug_scripts:
        if os.path.exists(script):
            print(f"🔧 清理调试脚本: {script}")
            safe_remove(script)
        else:
            print(f"✅ 调试脚本已不存在: {script}")

def cleanup_empty_directories():
    """清理空目录"""
    print_section("清理空目录")
    
    potential_empty_dirs = [
        "scripts/launchers",
        "src/web/routes/__pycache__",
        "__pycache__",
    ]
    
    for dir_path in potential_empty_dirs:
        if os.path.exists(dir_path):
            try:
                # 检查目录是否为空或只包含缓存文件
                if os.path.isdir(dir_path):
                    contents = os.listdir(dir_path)
                    if not contents or all(f.endswith('.pyc') or f == '__pycache__' for f in contents):
                        safe_remove(dir_path)
                    else:
                        print(f"📁 目录非空，保留: {dir_path}")
            except Exception as e:
                print(f"❌ 检查目录失败 {dir_path}: {e}")

def cleanup_spec_files():
    """清理构建规格文件"""
    print_section("清理构建规格文件")
    
    spec_files = [
        "optimized_minimal.spec",  # 旧的构建规格
    ]
    
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            print(f"📦 清理构建规格: {spec_file}")
            safe_remove(spec_file)
        else:
            print(f"✅ 构建规格已不存在: {spec_file}")

def update_project_config():
    """更新项目配置，移除已删除文件的引用"""
    print_section("更新项目配置")
    
    config_file = "project_config.py"
    if os.path.exists(config_file):
        print(f"🔧 更新配置文件: {config_file}")
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新脚本映射，移除已删除的文件
        updated_content = content.replace(
            '"debug": PROJECT_ROOT / "debug_api.py",',
            '# "debug": PROJECT_ROOT / "debug_api.py",  # 已删除'
        ).replace(
            '"test": PROJECT_ROOT / "final_test.py",',
            '# "test": PROJECT_ROOT / "final_test.py",  # 已删除'
        ).replace(
            '"quick_start": SCRIPTS_DIR / "quick_start.py"',
            '# "quick_start": SCRIPTS_DIR / "quick_start.py"  # 已删除'
        )
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ 项目配置已更新")
    else:
        print("⚠️ 项目配置文件不存在")

def main():
    """主清理函数"""
    print("🧹 亚马逊图片上传图床工具 - 重复脚本清理器")
    print("📋 本脚本将清理重复和无用的脚本文件")
    
    # 确认是否继续
    print("\n⚠️ 即将清理以下内容:")
    print("  - 重复的Web应用文件")
    print("  - 过时的启动脚本")
    print("  - 冗余的批处理文件")
    print("  - 临时调试脚本")
    print("  - 空目录和缓存文件")
    print("  - 旧的构建规格文件")
    
    confirm = input(f"\n是否继续清理? (y/n) [默认:y]: ")
    if confirm.lower() == 'n':
        print("❌ 用户取消清理")
        return False
    
    try:
        # 执行各种清理操作
        cleanup_duplicate_web_apps()
        cleanup_obsolete_scripts()
        cleanup_redundant_bat_files()
        cleanup_debug_scripts()
        cleanup_spec_files()
        cleanup_empty_directories()
        update_project_config()
        
        print_section("清理完成")
        print("🎉 重复脚本清理完成！")
        print("✅ 项目结构已优化")
        print("💡 现在请使用 'python main.py' 作为统一入口")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
